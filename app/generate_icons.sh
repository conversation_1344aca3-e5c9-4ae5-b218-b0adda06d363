#!/bin/bash

# 源图标路径
SOURCE_ICON="/Users/<USER>/Documents/omi/app/Memorion_Icon.png"
# 临时目录
TEMP_DIR="/Users/<USER>/Documents/omi/app/temp_icons"
# 目标目录
APP_ICON_DIR="/Users/<USER>/Documents/omi/app/ios/Runner/Assets.xcassets/AppIcon.appiconset"
DEV_ICON_DIR="/Users/<USER>/Documents/omi/app/ios/Runner/Assets.xcassets/devAppIcon.appiconset"
PROD_ICON_DIR="/Users/<USER>/Documents/omi/app/ios/Runner/Assets.xcassets/prodAppIcon.appiconset"

# 确保临时目录存在
mkdir -p "$TEMP_DIR"

# iOS图标尺寸
declare -a SIZES=(
  "20x20"    # 通知图标 1x (iPad)
  "40x40"    # 通知图标 2x (iPad), 聚光灯图标 1x (iPad)
  "60x60"    # 通知图标 3x (iPhone)
  "29x29"    # 设置图标 1x (iPad)
  "58x58"    # 设置图标 2x (iPad, iPhone)
  "87x87"    # 设置图标 3x (iPhone)
  "80x80"    # 聚光灯图标 2x (iPad, iPhone)
  "120x120"  # 聚光灯图标 3x (iPhone), 应用图标 2x (iPhone)
  "180x180"  # 应用图标 3x (iPhone)
  "76x76"    # 应用图标 1x (iPad)
  "152x152"  # 应用图标 2x (iPad)
  "167x167"  # 应用图标 2x (iPad Pro)
  "1024x1024" # App Store
)

# 生成不同尺寸的图标
for size in "${SIZES[@]}"; do
  width=${size%x*}
  height=${size#*x}
  output_file="$TEMP_DIR/icon_${width}x${height}.png"
  
  echo "生成 $size 图标..."
  sips -Z $width "$SOURCE_ICON" --out "$output_file"
done

# 复制到AppIcon.appiconset
echo "复制到AppIcon.appiconset..."
cp "$TEMP_DIR/icon_20x20.png" "$APP_ICON_DIR/OmiAppIcon-20@1x 1.png"
cp "$TEMP_DIR/icon_40x40.png" "$APP_ICON_DIR/OmiAppIcon-20@2x 1.png"
cp "$TEMP_DIR/icon_40x40.png" "$APP_ICON_DIR/<EMAIL>"
cp "$TEMP_DIR/icon_60x60.png" "$APP_ICON_DIR/<EMAIL>"
cp "$TEMP_DIR/icon_29x29.png" "$APP_ICON_DIR/OmiAppIcon-29@1x 1.png"
cp "$TEMP_DIR/icon_29x29.png" "$APP_ICON_DIR/<EMAIL>"
cp "$TEMP_DIR/icon_58x58.png" "$APP_ICON_DIR/OmiAppIcon-29@2x 1.png"
cp "$TEMP_DIR/icon_58x58.png" "$APP_ICON_DIR/<EMAIL>"
cp "$TEMP_DIR/icon_87x87.png" "$APP_ICON_DIR/<EMAIL>"
cp "$TEMP_DIR/icon_40x40.png" "$APP_ICON_DIR/OmiAppIcon-40@1x 1.png"
cp "$TEMP_DIR/icon_80x80.png" "$APP_ICON_DIR/OmiAppIcon-40@2x 1.png"
cp "$TEMP_DIR/icon_80x80.png" "$APP_ICON_DIR/<EMAIL>"
cp "$TEMP_DIR/icon_120x120.png" "$APP_ICON_DIR/<EMAIL>"
cp "$TEMP_DIR/icon_120x120.png" "$APP_ICON_DIR/OmiAppIcon-60@2x 1.png"
cp "$TEMP_DIR/icon_120x120.png" "$APP_ICON_DIR/<EMAIL>"
cp "$TEMP_DIR/icon_180x180.png" "$APP_ICON_DIR/OmiAppIcon-60@3x 1.png"
cp "$TEMP_DIR/icon_180x180.png" "$APP_ICON_DIR/<EMAIL>"
cp "$TEMP_DIR/icon_76x76.png" "$APP_ICON_DIR/OmiAppIcon-76@1x 1.png"
cp "$TEMP_DIR/icon_152x152.png" "$APP_ICON_DIR/OmiAppIcon-76@2x 1.png"
cp "$TEMP_DIR/icon_167x167.png" "$APP_ICON_DIR/OmiAppIcon-83.5@2x 1.png"
cp "$TEMP_DIR/icon_1024x1024.png" "$APP_ICON_DIR/OmiAppIcon-1024@1x 1.png"

# 复制到devAppIcon.appiconset
echo "复制到devAppIcon.appiconset..."
cp -r "$APP_ICON_DIR/"* "$DEV_ICON_DIR/"

# 复制到prodAppIcon.appiconset
echo "复制到prodAppIcon.appiconset..."
cp -r "$APP_ICON_DIR/"* "$PROD_ICON_DIR/"

echo "所有图标已生成并复制到相应目录！"
