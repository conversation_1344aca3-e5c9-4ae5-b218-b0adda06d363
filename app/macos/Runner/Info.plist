<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIconFile</key>
	<string></string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>app.memorion.me</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>https</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>app.memorion.me</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.208440318997-ukinsq3sijhcetkhr26ssqp1terbq7as</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSApplicationCategoryType</key>
	<string>public.app-category.productivity</string>
	<key>LSMinimumSystemVersion</key>
	<string>$(MACOSX_DEPLOYMENT_TARGET)</string>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>Bluetooth permission is required to connect with your Omi.</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>Bluetooth permission is required to connect with your Omi.</string>
	<key>NSCalendarsFullAccessUsageDescription</key>
	<string>Access most functions for calendar viewing and editing.</string>
	<key>NSCalendarsUsageDescription</key>
	<string>Access most functions for calendar viewing and editing.</string>
	<key>NSCameraUsageDescription</key>
	<string>Camera access is required to report issues</string>
	<key>NSContactsUsageDescription</key>
	<string>Access contacts for event attendee editing.</string>
	<key>NSHumanReadableCopyright</key>
	<string>$(PRODUCT_COPYRIGHT)</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Your location is used to tag where conversations take place, helping provide context in omi memory. This is optional, and your exact location is never stored or shared.</string>
	<key>NSLocationUsageDescription</key>
	<string>Your location is used to tag where conversations take place, helping provide context in omi memory. This is optional, and your exact location is never stored or shared..</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Your location is used to tag where conversations take place, helping provide context in omi memory. This is optional, and your exact location is never stored or shared.</string>
	<key>NSMainNibFile</key>
	<string>MainMenu</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>Omi records your microphone audio as part of conversation recording feature and transcribes in real-time</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>We need access to your photo library to allow you to upload and share photos through Instabug.</string>
	<key>NSPrincipalClass</key>
	<string>NSApplication</string>
	<key>PermissionGroupNotification</key>
	<string>You need to enable notifications to receive your pro-active feedback.</string>
	<key>LSUIElement</key>
	<false/>
	<key>LSBackgroundOnly</key>
	<false/>
	<key>NSSupportsAutomaticTermination</key>
	<false/>
	<key>NSSupportsSuddenTermination</key>
	<false/>
	<key>com.apple.security.app-sandbox</key>
	<true/>
	<key>com.apple.security.files.user-selected.read-only</key>
	<true/>
	<key>keychain-access-groups</key>
	<array>
		<string>$(AppIdentifierPrefix)com.google.GIDSignIn</string>
		<string>$(AppIdentifierPrefix)com.friend-app-with-wearable.ios12</string>
	</array>
</dict>
</plist>
